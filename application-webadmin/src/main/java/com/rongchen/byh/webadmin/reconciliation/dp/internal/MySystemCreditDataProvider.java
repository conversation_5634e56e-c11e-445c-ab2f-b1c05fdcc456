package com.rongchen.byh.webadmin.reconciliation.dp.internal;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.rongchen.byh.webadmin.reconciliation.context.ReconciliationContext;
import com.rongchen.byh.webadmin.reconciliation.dataProvider.DataProvider;
import com.rongchen.byh.webadmin.reconciliation.model.NormalizedCreditRecord;
import com.rongchen.byh.webadmin.upms.dao.ApiCreditRecordMapper;
import com.rongchen.byh.webadmin.upms.model.ApiCreditRecord;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * 我方系统授信数据提供者。
 */
@Component
@Slf4j
public class MySystemCreditDataProvider implements DataProvider {
    private static final Logger logger = LoggerFactory.getLogger(MySystemCreditDataProvider.class);
    private static final DateTimeFormatter OUR_SYSTEM_DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    private final ApiCreditRecordMapper apiCreditRecordMapper; // 使用正确的Mapper类型
    private final ObjectMapper objectMapper; // Jackson ObjectMapper，用于POJO到Map的转换

    @Autowired
    public MySystemCreditDataProvider(ApiCreditRecordMapper apiCreditRecordMapper, ObjectMapper objectMapper) {
        this.apiCreditRecordMapper = apiCreditRecordMapper;
        this.objectMapper = objectMapper;
        logger.info("MySystemCreditDataProvider 初始化完成，已配置 ApiCreditRecordMapper");
    }

    @Override
    public List<Map<String, Object>> loadData(ReconciliationContext context) throws Exception {
        throw new UnsupportedOperationException("请使用 loadNormalizedData() 方法获取我方系统的标准化授信数据。");
    }

    @Override
    public List<NormalizedCreditRecord> loadNormalizedData(ReconciliationContext context) throws Exception {
        logger.info("开始从我方系统加载并转换授信数据，处理日期: {}", context.getProcessingDate());

        try {
            // 计算查询日期范围（处理日期当天）
            LocalDate processingDate = context.getProcessingDate();
            Date startDate = Date.from(processingDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
            Date endDate = Date.from(processingDate.plusDays(1).atStartOfDay(ZoneId.systemDefault()).toInstant());

            // 查询我方授信数据：只查询授信成功的记录（creditStatus = 2）
            LambdaQueryWrapper<ApiCreditRecord> queryWrapper = new LambdaQueryWrapper<ApiCreditRecord>()
                    .eq(ApiCreditRecord::getCreditStatus, 2) // 只查询授信成功的记录
                    .ge(ApiCreditRecord::getCreateTime, startDate) // 创建时间 >= 处理日期开始
                    .lt(ApiCreditRecord::getCreateTime, endDate)   // 创建时间 < 处理日期+1天开始
                    .orderByAsc(ApiCreditRecord::getCreateTime);   // 按创建时间升序

            List<ApiCreditRecord> creditEntities = apiCreditRecordMapper.selectList(queryWrapper);

            if (CollectionUtils.isEmpty(creditEntities)) {
                logger.info("在我方系统未找到处理日期 {} 的授信成功数据。", context.getProcessingDate());
                return new ArrayList<>();
            }
            logger.info("从我方系统加载了 {} 条授信成功数据。", creditEntities.size());

            // 转换为标准化模型
            return creditEntities.stream()
                    .map(entity -> convertToNormalized(entity, context))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

        } catch (Exception e) {
            logger.error("从我方系统加载授信数据时发生错误: {}", e.getMessage(), e);
            throw new Exception("加载我方系统授信数据失败", e);
        }
    }

    private NormalizedCreditRecord convertToNormalized(ApiCreditRecord apiCreditRecord, ReconciliationContext context) {
        try {
            NormalizedCreditRecord norm = new NormalizedCreditRecord();

            // --- 映射关键字段 ---
            // 授信单号：我方系统的授信单号作为资方授信单号
            norm.setPartnerCreditNo(apiCreditRecord.getCreditNo());

            // 授信状态：将我方状态转换为标准状态
            // 我方：1-授信中, 2-授信成功, 3-授信拒绝
            // 标准：使用字符串表示，"2"表示授信成功
            norm.setCreditStatus(String.valueOf(apiCreditRecord.getCreditStatus()));

            // 授信日期：使用创建时间作为授信日期
            norm.setCreditDate(convertDateToLocalDate(apiCreditRecord.getCreateTime()));

            // 授信金额
            norm.setCreditAmount(apiCreditRecord.getCreditMoney() != null ?
                apiCreditRecord.getCreditMoney() : BigDecimal.ZERO);

            // 通用状态：使用授信状态的字符串形式
            norm.setStatus(String.valueOf(apiCreditRecord.getCreditStatus()));

            // --- 设置 NormalizedTransaction 接口字段 ---
            norm.setSourceChannel(getChannelCode()); // MYSYSTEM
            norm.setOriginalRecordId(String.valueOf(apiCreditRecord.getId()));

            // 初始化附加数据并存储其他有用信息
            Map<String, Object> additionalData = new HashMap<>();
            additionalData.put("userId", apiCreditRecord.getUserId());
            additionalData.put("outUserId", apiCreditRecord.getOutUserId());
            additionalData.put("channel", apiCreditRecord.getChannel());
            additionalData.put("sourceChannel", apiCreditRecord.getSourceChannel());
            additionalData.put("failReason", apiCreditRecord.getFailReason());
            additionalData.put("createTime", apiCreditRecord.getCreateTime());
            additionalData.put("updateTime", apiCreditRecord.getUpdateTime());
            norm.setAdditionalData(additionalData);

            logger.debug("成功转换我方授信记录: creditNo={}, creditStatus={}, creditAmount={}",
                apiCreditRecord.getCreditNo(), apiCreditRecord.getCreditStatus(), apiCreditRecord.getCreditMoney());

            return norm;
        } catch (Exception e) {
            logger.error("将我方授信实体 (ID: {}, creditNo: {}) 转换为NormalizedCreditRecord失败: {}",
                apiCreditRecord.getId(), apiCreditRecord.getCreditNo(), e.getMessage(), e);
            return null;
        }
    }

    /**
     * 将 Date 对象转换为 LocalDate
     */
    private LocalDate convertDateToLocalDate(Date date) {
        if (date == null) {
            return null;
        }
        return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
    }

    /**
     * 解析我方系统的日期字符串（保留原有方法以备用）
     */
    private LocalDate parseOurDate(String dateStr) {
        if (!StringUtils.hasText(dateStr))
            return null;
        try {
            return LocalDate.parse(dateStr, OUR_SYSTEM_DATE_FORMATTER);
        } catch (DateTimeParseException e) {
            logger.warn("解析我方日期字符串 '{}' (格式: yyyy-MM-dd) 失败: {}", dateStr, e.getMessage());
            return null;
        }
    }

    @Override
    public String getChannelCode() {
        return "MYSYSTEM";
    }

    @Override
    public String getTransactionType() {
        return "CREDIT";
    }

    @Override
    public String getDataSourceType() {
        return "OUR_SIDE";
    }
}