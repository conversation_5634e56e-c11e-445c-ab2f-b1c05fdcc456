package com.rongchen.byh.webadmin.reconciliation.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.rongchen.byh.webadmin.upms.model.DzRawPartnerCreditDataEntity;
import com.rongchen.byh.webadmin.upms.model.DzRawPartnerLoanDataEntity;
import com.rongchen.byh.webadmin.upms.model.DzRawPartnerRepaymentDataEntity;
import com.rongchen.byh.webadmin.upms.service.DzRawPartnerCreditDataService;
import com.rongchen.byh.webadmin.upms.service.DzRawPartnerLoanDataService;
import com.rongchen.byh.webadmin.upms.service.DzRawPartnerRepaymentDataService;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * 原始数据存储服务。
 * <p>
 * 负责将从SFTP等外部源下载并解析后的原始数据 (List&lt;Map&lt;String, Object&gt;&gt;)
 * 转换为对应的数据表实体并批量保存。
 */
@Service
public class RawDataStorageService {

    private static final Logger logger = LoggerFactory.getLogger(RawDataStorageService.class);
    private static final ObjectMapper objectMapper = new ObjectMapper(); // For JSON conversion
    // XHY渠道原始文件日期时间格式定义 (根据您提供的文档)
    private static final DateTimeFormatter XHY_DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static final DateTimeFormatter XHY_DATETIME_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
    private final DzRawPartnerLoanDataService dzRawLoanDataService;
    private final DzRawPartnerRepaymentDataService dzRawRepaymentDataService;
    private final DzRawPartnerCreditDataService dzRawCreditDataService;

    @Autowired
    public RawDataStorageService(DzRawPartnerLoanDataService dzRawLoanDataService,
            DzRawPartnerRepaymentDataService dzRawRepaymentDataService,
            DzRawPartnerCreditDataService dzRawCreditDataService) {
        this.dzRawLoanDataService = dzRawLoanDataService;
        this.dzRawRepaymentDataService = dzRawRepaymentDataService;
        this.dzRawCreditDataService = dzRawCreditDataService;
    }

    /**
     * 存储原始数据列表。
     *
     * @param syncBatchId     SFTP同步批次ID。
     * @param channelCode     渠道编码。
     * @param transactionType 交易类型 ("LOAN" 或 "REPAYMENT")。
     * @param processingDate  原始数据对应的业务处理日期。
     * @param rawDataList     从DataProvider获取的原始数据列表。
     */
    public void storeRawData(String syncBatchId, String channelCode, String transactionType,
            LocalDate processingDate, List<Map<String, Object>> rawDataList) {
        if (CollectionUtils.isEmpty(rawDataList)) {
            logger.info("没有原始数据需要为批次 {} (渠道: {}, 类型: {}) 存储。", syncBatchId, channelCode, transactionType);
            return;
        }

        logger.info("开始为批次 {} (渠道: {}, 类型: {}) 存储 {} 条原始数据...",
                syncBatchId, channelCode, transactionType, rawDataList.size());

        if ("LOAN".equalsIgnoreCase(transactionType)) {
            loanRawData(syncBatchId, channelCode, processingDate, rawDataList);
            return;
        }

        if ("REPAYMENT".equalsIgnoreCase(transactionType)) {
            repaymentRawData(syncBatchId, channelCode, processingDate, rawDataList);
            return;
        }
        if ("CREDIT".equalsIgnoreCase(transactionType)) {
            List<DzRawPartnerCreditDataEntity> creditEntities = new ArrayList<>();
            for (Map<String, Object> rawRecord : rawDataList) {
                try {
                    creditEntities
                            .add(convertToCreditEntity(rawRecord, syncBatchId, channelCode, processingDate));
                } catch (Exception e) {
                    logger.error("原始还款记录转换为实体失败，将跳过此记录。批次ID: {}, 记录: {}. 错误: {}",
                            syncBatchId, rawRecord, e.getMessage(), e);
                }
            }
            if (!creditEntities.isEmpty()) {
                boolean success = dzRawCreditDataService.saveBatch(creditEntities);
                if (success) {
                    logger.info("成功为批次 {} 存储 {} 条原始还款数据。", syncBatchId, creditEntities.size());
                } else {
                    logger.error("为批次 {} 批量存储原始还款数据失败。", syncBatchId);
                }
            }
        }

        logger.warn("不支持的交易类型进行原始数据存储: {}。批次ID: {}", transactionType, syncBatchId);

    }

    private void repaymentRawData(String syncBatchId, String channelCode, LocalDate processingDate,
            List<Map<String, Object>> rawDataList) {
        List<DzRawPartnerRepaymentDataEntity> repaymentEntities = new ArrayList<>();
        for (Map<String, Object> rawRecord : rawDataList) {
            try {
                repaymentEntities.add(convertToRepaymentEntity(rawRecord, syncBatchId, channelCode, processingDate));
            } catch (Exception e) {
                logger.error("原始还款记录转换为实体失败，将跳过此记录。批次ID: {}, 记录: {}. 错误: {}",
                        syncBatchId, rawRecord, e.getMessage(), e);
            }
        }
        if (!repaymentEntities.isEmpty()) {
            // 使用 UPSERT 策略替代直接插入
            UpsertResult result = upsertRepaymentData(repaymentEntities);
            logger.info("批次 {} 还款数据处理完成: 新增 {} 条, 更新 {} 条, 跳过 {} 条",
                    syncBatchId, result.getInsertedCount(), result.getUpdatedCount(), result.getSkippedCount());
        }
    }

    private void loanRawData(String syncBatchId, String channelCode, LocalDate processingDate,
            List<Map<String, Object>> rawDataList) {
        List<DzRawPartnerLoanDataEntity> loanEntities = new ArrayList<>();
        for (Map<String, Object> rawRecord : rawDataList) {
            try {
                loanEntities.add(convertToLoanEntity(rawRecord, syncBatchId, channelCode, processingDate));
            } catch (Exception e) {
                logger.error("原始借款记录转换为实体失败，将跳过此记录。批次ID: {}, 记录: {}. 错误: {}",
                        syncBatchId, rawRecord, e.getMessage(), e);
            }
        }
        if (!loanEntities.isEmpty()) {
            // 使用 UPSERT 策略替代直接插入
            UpsertResult result = upsertLoanData(loanEntities);
            logger.info("批次 {} 借款数据处理完成: 新增 {} 条, 更新 {} 条, 跳过 {} 条",
                    syncBatchId, result.getInsertedCount(), result.getUpdatedCount(), result.getSkippedCount());
        }
    }

    private DzRawPartnerLoanDataEntity convertToLoanEntity(Map<String, Object> rawRecord, String syncBatchId,
            String channelCode, LocalDate processingDate) throws JsonProcessingException {
        DzRawPartnerLoanDataEntity entity = new DzRawPartnerLoanDataEntity();
        entity.setSyncBatchId(syncBatchId);
        entity.setChannelCode(channelCode);
        if (processingDate != null) {
            entity.setProcessingDate(java.sql.Date.valueOf(processingDate));
        }
        entity.setTransactionType("LOAN");
        entity.setLoadTime(java.sql.Timestamp.valueOf(LocalDateTime.now()));

        entity.setPartnerLoanOrderNo(getString(rawRecord, "transSeqno"));
        LocalDate submissionDate = parseLocalDate(rawRecord, "transDate", XHY_DATE_FORMATTER);
        if (submissionDate != null) {
            entity.setSubmissionDate(java.sql.Date.valueOf(submissionDate));
        }
        LocalDate fundingDate = parseLocalDate(rawRecord, "payDate", XHY_DATE_FORMATTER);
        if (fundingDate != null) {
            entity.setFundingDate(java.sql.Date.valueOf(fundingDate));
        }
        LocalDate maturityDate = parseLocalDate(rawRecord, "endDate", XHY_DATE_FORMATTER);
        if (maturityDate != null) {
            entity.setMaturityDate(java.sql.Date.valueOf(maturityDate));
        }
        entity.setPrincipalAmount(parseBigDecimal(rawRecord, "transAmt"));
        entity.setTermCount(parseInteger(rawRecord, "totalCnt"));
        entity.setInterestRateStr(getString(rawRecord, "rate"));
        entity.setRepaymentTypeCode(getString(rawRecord, "repayType"));
        entity.setFundingProviderCode(getString(rawRecord, "capCode"));

        Map<String, Object> extendedFields = new HashMap<>(rawRecord);
        Set<String> fixedLoanFields = new HashSet<>(Arrays.asList(
                "transSeqno", "transDate", "payDate", "endDate", "transAmt",
                "totalCnt", "rate", "repayType", "capCode"));
        extendedFields.keySet().removeAll(fixedLoanFields);

        if (!extendedFields.isEmpty()) {
            entity.setExtendedFieldsJson(objectMapper.writeValueAsString(extendedFields));
        }
        return entity;
    }

    private DzRawPartnerRepaymentDataEntity convertToRepaymentEntity(Map<String, Object> rawRecord, String syncBatchId,
            String channelCode, LocalDate processingDate) throws JsonProcessingException {
        DzRawPartnerRepaymentDataEntity entity = new DzRawPartnerRepaymentDataEntity();
        entity.setSyncBatchId(syncBatchId);
        entity.setChannelCode(channelCode);
        if (processingDate != null) {
            entity.setProcessingDate(java.sql.Date.valueOf(processingDate));
        }
        entity.setTransactionType("REPAYMENT");
        entity.setLoadTime(java.sql.Timestamp.valueOf(LocalDateTime.now()));

        entity.setPartnerLoanOrderNo(getString(rawRecord, "transSeqno"));
        entity.setPartnerRepaymentId(getString(rawRecord, "repaySeqNo"));
        LocalDateTime submissionDateTime = parseFlexibleDateTime(rawRecord, "transDate");
        if (submissionDateTime != null) {
            entity.setSubmissionDatetime(java.sql.Timestamp.valueOf(submissionDateTime));
        }
        LocalDateTime effectiveDate = parseFlexibleDateTime(rawRecord, "repayEndDate");
        if (effectiveDate != null) {
            entity.setRepaymentEffectiveDate(java.sql.Timestamp.valueOf(effectiveDate));
        }
        entity.setInstallmentNumbersStr(getString(rawRecord, "installCnt"));
        entity.setRepaymentMethodCode(getString(rawRecord, "repayMode"));
        entity.setPaymentChannelCode(getString(rawRecord, "repayChannel"));
        entity.setDebtTransferStatusCode(getString(rawRecord, "CompensatoryStatus"));
        entity.setRepaymentTypeCode(getString(rawRecord, "repayType"));
        entity.setTotalRepaidAmount(parseBigDecimal(rawRecord, "repayAmt"));
        entity.setRepaidPrincipalAmount(parseBigDecimal(rawRecord, "repayPrincipal"));
        entity.setRepaidInterestAmount(parseBigDecimal(rawRecord, "repayInterest"));
        entity.setRepaidFeeAmount(parseBigDecimal(rawRecord, "repayFee"));
        entity.setRepaidOverdueAmount(parseBigDecimal(rawRecord, "repayOverdueInterest"));

        Map<String, Object> extendedFields = new HashMap<>(rawRecord);
        Set<String> fixedFields = new HashSet<>(Arrays.asList(
                "transSeqno", "repaySeqNo", "transDate", "repayEndDate", "installCnt",
                "repayMode", "repayChannel", "CompensatoryStatus", "repayType", "repayAmt",
                "repayPrincipal", "repayInterest", "repayFee", "repayOverdueInterest"));
        extendedFields.keySet().removeAll(fixedFields);

        if (!extendedFields.isEmpty()) {
            entity.setExtendedFieldsJson(objectMapper.writeValueAsString(extendedFields));
        }
        return entity;
    }

    private DzRawPartnerCreditDataEntity convertToCreditEntity(Map<String, Object> rawRecord, String syncBatchId,
            String channelCode, LocalDate processingDate) throws JsonProcessingException {
        DzRawPartnerCreditDataEntity entity = new DzRawPartnerCreditDataEntity();
        entity.setSyncBatchId(syncBatchId);
        entity.setChannelCode(channelCode);
        if (processingDate != null) {
            entity.setProcessingDate(java.sql.Date.valueOf(processingDate));
        }
        entity.setTransactionType("CREDIT");
        entity.setLoadTime(java.sql.Timestamp.valueOf(LocalDateTime.now()));

        entity.setPartnerCreditNo(getString(rawRecord, "creditNo"));
        LocalDateTime creditDate = parseLocalDateTime(rawRecord, "creditDate", XHY_DATETIME_FORMATTER);
        if (creditDate != null) {
            entity.setCreditDate(java.sql.Timestamp.valueOf(creditDate));
        }

        Map<String, Object> extendedFields = new HashMap<>(rawRecord);
        extendedFields.remove("creditNo");
        extendedFields.remove("creditDate");

        if (!extendedFields.isEmpty()) {
            entity.setExtendedFieldsJson(objectMapper.writeValueAsString(extendedFields));
        }
        return entity;
    }

    /**
     * 基于业务主键的借款数据 UPSERT 操作（批量优化版本）
     *
     * @param loanEntities 要处理的借款数据列表
     * @return UPSERT 操作结果统计
     */
    private UpsertResult upsertLoanData(List<DzRawPartnerLoanDataEntity> loanEntities) {
        if (CollectionUtils.isEmpty(loanEntities)) {
            return new UpsertResult(0, 0, 0);
        }

        int insertedCount = 0;
        int updatedCount = 0;
        int skippedCount = 0;

        // 1. 过滤掉缺少业务主键的记录
        List<DzRawPartnerLoanDataEntity> validEntities = new ArrayList<>();
        for (DzRawPartnerLoanDataEntity entity : loanEntities) {
            if (!StringUtils.hasText(entity.getPartnerLoanOrderNo())) {
                logger.warn("借款记录缺少业务主键 partner_loan_order_no，跳过处理");
                skippedCount++;
                continue;
            }
            validEntities.add(entity);
        }

        if (validEntities.isEmpty()) {
            return new UpsertResult(insertedCount, updatedCount, skippedCount);
        }

        try {
            // 2. 提取所有业务主键，批量查询现有记录
            Set<String> businessKeys = new HashSet<>();
            for (DzRawPartnerLoanDataEntity entity : validEntities) {
                businessKeys.add(generateLoanBusinessKey(entity.getPartnerLoanOrderNo(), entity.getChannelCode()));
            }

            List<DzRawPartnerLoanDataEntity> existingEntities = dzRawLoanDataService.list(
                    new LambdaQueryWrapper<DzRawPartnerLoanDataEntity>()
                            .in(DzRawPartnerLoanDataEntity::getPartnerLoanOrderNo,
                                validEntities.stream().map(DzRawPartnerLoanDataEntity::getPartnerLoanOrderNo).collect(Collectors.toSet()))
                            .in(DzRawPartnerLoanDataEntity::getChannelCode,
                                validEntities.stream().map(DzRawPartnerLoanDataEntity::getChannelCode).collect(Collectors.toSet())));

            // 3. 转换为 Map 便于快速查找
            Map<String, DzRawPartnerLoanDataEntity> existingMap = new HashMap<>();
            for (DzRawPartnerLoanDataEntity existing : existingEntities) {
                String key = generateLoanBusinessKey(existing.getPartnerLoanOrderNo(), existing.getChannelCode());
                existingMap.put(key, existing);
            }

            // 4. 分离新增和更新数据
            List<DzRawPartnerLoanDataEntity> toInsert = new ArrayList<>();
            List<DzRawPartnerLoanDataEntity> toUpdate = new ArrayList<>();

            for (DzRawPartnerLoanDataEntity newEntity : validEntities) {
                String businessKey = generateLoanBusinessKey(newEntity.getPartnerLoanOrderNo(), newEntity.getChannelCode());
                DzRawPartnerLoanDataEntity existingEntity = existingMap.get(businessKey);

                if (existingEntity == null) {
                    // 不存在，加入新增列表
                    toInsert.add(newEntity);
                } else {
                    // 存在，更新字段后加入更新列表
                    updateLoanEntityFields(existingEntity, newEntity);
                    toUpdate.add(existingEntity);
                }
            }

            // 5. 批量执行新增操作
            if (!toInsert.isEmpty()) {
                insertedCount = dzRawLoanDataService.batchInsert(toInsert);
                logger.debug("批量新增借款记录: {} 条", insertedCount);
            }

            // 6. 批量执行更新操作（使用 MyBatis-Plus 的 updateBatchById）
            if (!toUpdate.isEmpty()) {
                boolean updateResult = dzRawLoanDataService.updateBatchById(toUpdate);
                if (updateResult) {
                    updatedCount = toUpdate.size();
                    logger.debug("批量更新借款记录: {} 条", updatedCount);
                } else {
                    logger.error("批量更新借款记录失败");
                    skippedCount += toUpdate.size();
                }
            }

        } catch (Exception e) {
            logger.error("批量处理借款记录时发生错误: {}", e.getMessage(), e);
            // 发生异常时，将所有有效记录计为跳过
            skippedCount += validEntities.size();
            insertedCount = 0;
            updatedCount = 0;
        }

        return new UpsertResult(insertedCount, updatedCount, skippedCount);
    }

    /**
     * 基于业务主键的还款数据 UPSERT 操作（批量优化版本）
     *
     * @param repaymentEntities 要处理的还款数据列表
     * @return UPSERT 操作结果统计
     */
    private UpsertResult upsertRepaymentData(List<DzRawPartnerRepaymentDataEntity> repaymentEntities) {
        if (CollectionUtils.isEmpty(repaymentEntities)) {
            return new UpsertResult(0, 0, 0);
        }

        int insertedCount = 0;
        int updatedCount = 0;
        int skippedCount = 0;

        // 1. 过滤掉缺少业务主键的记录
        List<DzRawPartnerRepaymentDataEntity> validEntities = new ArrayList<>();
        for (DzRawPartnerRepaymentDataEntity entity : repaymentEntities) {
            if (!StringUtils.hasText(entity.getPartnerRepaymentId())) {
                logger.warn("还款记录缺少业务主键 partner_repayment_id，跳过处理");
                skippedCount++;
                continue;
            }
            validEntities.add(entity);
        }

        if (validEntities.isEmpty()) {
            return new UpsertResult(insertedCount, updatedCount, skippedCount);
        }

        try {
            // 2. 提取所有业务主键，批量查询现有记录
            Set<String> businessKeys = new HashSet<>();
            for (DzRawPartnerRepaymentDataEntity entity : validEntities) {
                businessKeys.add(generateRepaymentBusinessKey(entity.getPartnerRepaymentId(), entity.getChannelCode()));
            }

            List<DzRawPartnerRepaymentDataEntity> existingEntities = dzRawRepaymentDataService.list(
                    new LambdaQueryWrapper<DzRawPartnerRepaymentDataEntity>()
                            .in(DzRawPartnerRepaymentDataEntity::getPartnerRepaymentId,
                                validEntities.stream().map(DzRawPartnerRepaymentDataEntity::getPartnerRepaymentId).collect(Collectors.toSet()))
                            .in(DzRawPartnerRepaymentDataEntity::getChannelCode,
                                validEntities.stream().map(DzRawPartnerRepaymentDataEntity::getChannelCode).collect(Collectors.toSet())));

            // 3. 转换为 Map 便于快速查找
            Map<String, DzRawPartnerRepaymentDataEntity> existingMap = new HashMap<>();
            for (DzRawPartnerRepaymentDataEntity existing : existingEntities) {
                String key = generateRepaymentBusinessKey(existing.getPartnerRepaymentId(), existing.getChannelCode());
                existingMap.put(key, existing);
            }

            // 4. 分离新增和更新数据
            List<DzRawPartnerRepaymentDataEntity> toInsert = new ArrayList<>();
            List<DzRawPartnerRepaymentDataEntity> toUpdate = new ArrayList<>();

            for (DzRawPartnerRepaymentDataEntity newEntity : validEntities) {
                String businessKey = generateRepaymentBusinessKey(newEntity.getPartnerRepaymentId(), newEntity.getChannelCode());
                DzRawPartnerRepaymentDataEntity existingEntity = existingMap.get(businessKey);

                if (existingEntity == null) {
                    // 不存在，加入新增列表
                    toInsert.add(newEntity);
                } else {
                    // 存在，更新字段后加入更新列表
                    updateRepaymentEntityFields(existingEntity, newEntity);
                    toUpdate.add(existingEntity);
                }
            }

            // 5. 批量执行新增操作
            if (!toInsert.isEmpty()) {
                insertedCount = dzRawRepaymentDataService.batchInsert(toInsert);
                logger.debug("批量新增还款记录: {} 条", insertedCount);
            }

            // 6. 批量执行更新操作（使用 MyBatis-Plus 的 updateBatchById）
            if (!toUpdate.isEmpty()) {
                boolean updateResult = dzRawRepaymentDataService.updateBatchById(toUpdate);
                if (updateResult) {
                    updatedCount = toUpdate.size();
                    logger.debug("批量更新还款记录: {} 条", updatedCount);
                } else {
                    logger.error("批量更新还款记录失败");
                    skippedCount += toUpdate.size();
                }
            }

        } catch (Exception e) {
            logger.error("批量处理还款记录时发生错误: {}", e.getMessage(), e);
            // 发生异常时，将所有有效记录计为跳过
            skippedCount += validEntities.size();
            insertedCount = 0;
            updatedCount = 0;
        }

        return new UpsertResult(insertedCount, updatedCount, skippedCount);
    }

    /**
     * 更新借款实体的可变字段
     */
    private void updateLoanEntityFields(DzRawPartnerLoanDataEntity existing, DzRawPartnerLoanDataEntity newEntity) {
        // 更新同步批次ID（重要：标识最新的同步批次）
        existing.setSyncBatchId(newEntity.getSyncBatchId());

        // 更新金额相关字段（可能发生变化）
        existing.setPrincipalAmount(newEntity.getPrincipalAmount());
        existing.setInterestRateStr(newEntity.getInterestRateStr());

        // 更新日期字段（可能有修正）
        existing.setSubmissionDate(newEntity.getSubmissionDate());
        existing.setFundingDate(newEntity.getFundingDate());
        existing.setMaturityDate(newEntity.getMaturityDate());

        // 更新其他可变字段
        existing.setTermCount(newEntity.getTermCount());
        existing.setRepaymentTypeCode(newEntity.getRepaymentTypeCode());
        existing.setFundingProviderCode(newEntity.getFundingProviderCode());
        existing.setExtendedFieldsJson(newEntity.getExtendedFieldsJson());

        // 更新加载时间
        existing.setLoadTime(newEntity.getLoadTime());
    }

    /**
     * 更新还款实体的可变字段
     */
    private void updateRepaymentEntityFields(DzRawPartnerRepaymentDataEntity existing, DzRawPartnerRepaymentDataEntity newEntity) {
        // 更新同步批次ID（重要：标识最新的同步批次）
        existing.setSyncBatchId(newEntity.getSyncBatchId());

        // 更新金额相关字段（可能发生变化）
        existing.setTotalRepaidAmount(newEntity.getTotalRepaidAmount());
        existing.setRepaidPrincipalAmount(newEntity.getRepaidPrincipalAmount());
        existing.setRepaidInterestAmount(newEntity.getRepaidInterestAmount());
        existing.setRepaidFeeAmount(newEntity.getRepaidFeeAmount());
        existing.setRepaidOverdueAmount(newEntity.getRepaidOverdueAmount());

        // 更新日期字段（可能有修正）
        existing.setSubmissionDatetime(newEntity.getSubmissionDatetime());
        existing.setRepaymentEffectiveDate(newEntity.getRepaymentEffectiveDate());

        // 更新其他可变字段
        existing.setInstallmentNumbersStr(newEntity.getInstallmentNumbersStr());
        existing.setRepaymentMethodCode(newEntity.getRepaymentMethodCode());
        existing.setPaymentChannelCode(newEntity.getPaymentChannelCode());
        existing.setDebtTransferStatusCode(newEntity.getDebtTransferStatusCode());
        existing.setRepaymentTypeCode(newEntity.getRepaymentTypeCode());
        existing.setExtendedFieldsJson(newEntity.getExtendedFieldsJson());

        // 更新加载时间
        existing.setLoadTime(newEntity.getLoadTime());
    }

    /**
     * 生成借款数据的业务主键
     *
     * @param partnerLoanOrderNo 资方借款单号
     * @param channelCode 渠道编码
     * @return 业务主键字符串
     */
    private String generateLoanBusinessKey(String partnerLoanOrderNo, String channelCode) {
        return partnerLoanOrderNo + "|" + channelCode;
    }

    /**
     * 生成还款数据的业务主键
     *
     * @param partnerRepaymentId 资方还款流水号
     * @param channelCode 渠道编码
     * @return 业务主键字符串
     */
    private String generateRepaymentBusinessKey(String partnerRepaymentId, String channelCode) {
        return partnerRepaymentId + "|" + channelCode;
    }

    /**
     * UPSERT 操作结果统计
     */
    private static class UpsertResult {
        private final int insertedCount;
        private final int updatedCount;
        private final int skippedCount;

        public UpsertResult(int insertedCount, int updatedCount, int skippedCount) {
            this.insertedCount = insertedCount;
            this.updatedCount = updatedCount;
            this.skippedCount = skippedCount;
        }

        public int getInsertedCount() { return insertedCount; }
        public int getUpdatedCount() { return updatedCount; }
        public int getSkippedCount() { return skippedCount; }
    }

    // --- 辅助转换方法 ---
    private String getString(Map<String, Object> record, String key) {
        Object value = record.get(key);
        return value != null ? value.toString() : null;
    }

    private BigDecimal parseBigDecimal(Map<String, Object> record, String key) {
        Object value = record.get(key);
        if (value == null || value.toString().trim().isEmpty())
            return null;
        try {
            return new BigDecimal(value.toString().trim());
        } catch (NumberFormatException e) {
            logger.warn("字段 '{}' 的值 '{}' 转换为BigDecimal失败: {}", key, value, e.getMessage());
            return null;
        }
    }

    private Integer parseInteger(Map<String, Object> record, String key) {
        Object value = record.get(key);
        if (value == null || value.toString().trim().isEmpty())
            return null;
        try {
            return Integer.parseInt(value.toString().trim());
        } catch (NumberFormatException e) {
            logger.warn("字段 '{}' 的值 '{}' 转换为Integer失败: {}", key, value, e.getMessage());
            return null;
        }
    }

    private LocalDate parseLocalDate(Map<String, Object> record, String key, DateTimeFormatter formatter) {
        Object value = record.get(key);
        if (value == null || value.toString().trim().isEmpty())
            return null;
        try {
            return LocalDate.parse(value.toString().trim(), formatter);
        } catch (DateTimeParseException e) {
            logger.warn("字段 '{}' 的值 '{}' 使用格式 '{}' 转换为LocalDate失败: {}", key, value, formatter.toString(),
                    e.getMessage());
            return null;
        }
    }

    private LocalDateTime parseLocalDateTime(Map<String, Object> record, String key, DateTimeFormatter formatter) {
        Object value = record.get(key);
        if (value == null || value.toString().trim().isEmpty())
            return null;
        try {
            return LocalDateTime.parse(value.toString().trim(), formatter);
        } catch (DateTimeParseException e) {
            logger.warn("字段 '{}' 的值 '{}' 使用格式 '{}' 转换为LocalDateTime失败: {}", key, value, formatter.toString(),
                    e.getMessage());
            return null;
        }
    }

    /**
     * 智能解析日期时间字段，支持多种格式的兼容性。
     * 根据字符串长度自动判断使用哪种格式：
     * - 8位字符（yyyyMMdd）：解析为LocalDate，转换为当天开始时间的LocalDateTime
     * - 14位字符（yyyyMMddHHmmss）：直接解析为LocalDateTime
     *
     * @param record 原始数据记录
     * @param key 字段名
     * @return 解析后的LocalDateTime，解析失败时返回null
     */
    private LocalDateTime parseFlexibleDateTime(Map<String, Object> record, String key) {
        Object value = record.get(key);
        if (value == null || value.toString().trim().isEmpty()) {
            return null;
        }

        String strValue = value.toString().trim();
        try {
            if (strValue.length() == 8) {
                // 8位字符，使用日期格式（yyyyMMdd）
                LocalDate date = LocalDate.parse(strValue, XHY_DATE_FORMATTER);
                return date.atStartOfDay(); // 转换为当天开始时间
            } else if (strValue.length() == 14) {
                // 14位字符，使用日期时间格式（yyyyMMddHHmmss）
                return LocalDateTime.parse(strValue, XHY_DATETIME_FORMATTER);
            } else {
                logger.warn("字段 '{}' 的值 '{}' 长度不符合预期格式（8位日期或14位日期时间）", key, strValue);
                return null;
            }
        } catch (DateTimeParseException e) {
            logger.warn("字段 '{}' 的值 '{}' 智能解析为LocalDateTime失败: {}", key, strValue, e.getMessage());
            return null;
        }
    }
}