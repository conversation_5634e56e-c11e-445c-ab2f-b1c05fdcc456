package com.rongchen.byh.webadmin.reconciliation.job;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.rongchen.byh.webadmin.WebAdminBaseTest;
import com.rongchen.byh.webadmin.reconciliation.enums.TransactionTypeEnum;
import com.rongchen.byh.webadmin.reconciliation.service.ReconciliationRetryService;
import com.rongchen.byh.webadmin.reconciliation.service.ReconciliationRetryService.RetryResult;
import com.rongchen.byh.webadmin.upms.model.DzChannelReconConfigEntity;
import com.rongchen.byh.webadmin.upms.service.DzChannelReconConfigService;
import java.time.LocalDate;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

/**
 * 项目名称：byh_java
 * 文件名称: ReconciliationJobTest
 * 创建时间: 2025-05-23 11:13
 * 创建人: XuYu
 * 所属包名: com.rongchen.byh.webadmin.reconciliation.job
 * 文件描述:
 * Copyright (c) 2025 byh_java All rights reserved.
 */
@Slf4j
public class ReconciliationJobTest extends WebAdminBaseTest {

    @Autowired
    private ReconciliationJob reconciliationJob;

    @Autowired
    private ReconciliationRetryService reconciliationRetryService;
    @Autowired
    private DzChannelReconConfigService dzChannelReconConfigService;

    /**
     * 执行每日对账。
     */
    @Test
    @DisplayName("测试执行每日对账")
    public void testRunDailyReconciliation() {
        // 还款日 2025-5-26
        LocalDate processingDate = LocalDate.of(2025, 6, 2); // 通常对账前一天的数据

        List<DzChannelReconConfigEntity> reconConfigs = dzChannelReconConfigService.list(
            new LambdaQueryWrapper<DzChannelReconConfigEntity>()
                .eq(DzChannelReconConfigEntity::getReconEnabled, true));

        if (CollectionUtils.isEmpty(reconConfigs)) {
            log.info("日期 {} 没有找到需要执行对账的已启用渠道配置。", processingDate);
            return;
        }
        log.info("日期 {} 发现 {} 条已启用的对账配置项。", processingDate, reconConfigs.size());
        reconciliationJob.processDailyReconciliationForConfigs(processingDate, reconConfigs, null);

    }

    /**
     * 月度对账任务测试。
     */
    @Test
    @DisplayName("测试执行月度对账任务")
    public void testRunMonthlyReconciliation() {
        reconciliationJob.runMonthlyReconciliation();
    }

    /**
     * 按指定日期重试对账。
     */
    @Test
    @DisplayName("测试按指定日期重试对账")
    public void testRetryByDate() {

        RetryResult retryResult = reconciliationRetryService.retryByDate(LocalDate.of(2025, 5, 2), "测试重试");
        log.info("按指定日期重试对账 重试结果: {}", retryResult);

    }

    /**
     * 按批次ID重试对账。
     */
    @Test
    @DisplayName("测试按批次ID重试对账")
    public void testRetryByBatchId() {
        RetryResult retryResult = reconciliationRetryService.retryByBatchId("BATCH_20250602_XHY_REPAYMENT_E465CDCE",
            "测试重试");
        log.info("按批次ID重试对账 重试结果: {}", retryResult);
    }

    /**
     * 按指定日期、渠道编码和交易类型重试对账。
     */
    @Test
    @DisplayName("测试按指定日期、渠道编码和交易类型重试对账")
    public void testRetryByDateChannelType() {
        RetryResult retryResult = reconciliationRetryService.retryByDateChannelType(LocalDate.of(2025, 6, 2), "XHY",
            TransactionTypeEnum.REPAYMENT.toString(), "测试重试");
        log.info("按指定日期、渠道编码和交易类型重试对账 重试结果: {}", retryResult);
    }

}
